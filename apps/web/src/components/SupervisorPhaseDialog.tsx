import { <PERSON><PERSON>, DialogContent, DialogFooter, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { SUPERVISOR_SUBTASK_LABELS, SupervisorSubTask } from '@/types/project';

export type PhaseStatus = 'not_started' | 'booked' | 'pending' | 'in_progress' | 'complete' | 'defect';
export type PhaseStateRecord = Record<string, { status: PhaseStatus }>; // key is SupervisorSubTask

const STATUS_LABEL: Record<PhaseStatus, string> = {
  not_started: 'Not Started',
  booked: 'Booked',
  pending: 'Pending',
  in_progress: 'In Progress',
  complete: 'Complete',
  defect: 'Defect',
};

const STATUS_TONE: Record<PhaseStatus, string> = {
  not_started: 'bg-muted text-muted-foreground',
  booked: 'bg-blue-100 text-blue-700 dark:bg-blue-950 dark:text-blue-300',
  pending: 'bg-amber-100 text-amber-700 dark:bg-amber-950 dark:text-amber-300',
  in_progress: 'bg-sky-100 text-sky-700 dark:bg-sky-950 dark:text-sky-300',
  complete: 'bg-emerald-100 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-300',
  defect: 'bg-rose-100 text-rose-700 dark:bg-rose-950 dark:text-rose-300',
};

export function getAllowedTransitions(current: PhaseStatus): PhaseStatus[] {
  switch (current) {
    case 'not_started': return ['booked'];
    case 'booked': return ['pending'];
    case 'pending': return ['in_progress'];
    case 'in_progress': return ['complete', 'defect'];
    case 'complete': return ['defect'];
    case 'defect': return ['in_progress'];
    default: return [];
  }
}

export function SupervisorPhaseDialog({
  open,
  onOpenChange,
  selectedPhases,
  phaseStates,
  onChange,
  onTransition,
}: {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  selectedPhases: SupervisorSubTask[];
  phaseStates: PhaseStateRecord;
  onChange: (next: PhaseStateRecord) => void;
  onTransition?: (phaseKey: SupervisorSubTask, to: PhaseStatus) => void;
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Update Subtask Status</DialogTitle>
        </DialogHeader>

        <div className="space-y-2 max-h-[60vh] overflow-auto pr-1">
          {selectedPhases.length === 0 && (
            <div className="text-sm text-muted-foreground">No phases selected.</div>
          )}
          {selectedPhases.map((phase) => {
            const current = (phaseStates[phase]?.status ?? 'not_started') as PhaseStatus;
            const allowed = getAllowedTransitions(current);
            return (
              <div key={phase} className="flex items-center gap-3 p-2 rounded border">
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate" title={(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}>
                    {(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}
                  </div>
                  <div className="text-xs text-muted-foreground">Current: {STATUS_LABEL[current]}</div>
                </div>
                <Badge className={`shrink-0 ${STATUS_TONE[current]}`}>{STATUS_LABEL[current]}</Badge>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" variant="outline">Change status</Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {allowed.map((to) => (
                      <DropdownMenuItem key={to} onClick={() => { onChange({ ...phaseStates, [phase]: { status: to } }); onTransition?.(phase, to); }}>
                        {STATUS_LABEL[to]}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            );
          })}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

