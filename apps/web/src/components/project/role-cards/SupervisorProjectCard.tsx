import {
  Project,
  User,
  UserRole,
  ProjectStatus,
  SupervisorSubTask,
  canModifyTask,
  isManager,
} from "@/types/project";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/datetime";
import { Progress } from "@/components/ui/progress";
import { StatusBadge } from "@/components/StatusBadge";
import { statusToTone, shouldHideActionButtons } from "@/lib/status";
import { Calendar, User as UserIcon, Eye, ArrowRight } from "lucide-react";
import { SupervisorAssignDialog } from "@/components/SupervisorAssignDialog";
import { useState } from "react";
import { SupervisorPhaseDialog, PhaseStateRecord } from "@/components/SupervisorPhaseDialog";
import { Api } from "@/lib/api";

interface SupervisorProjectCardProps {
  project: Project;
  userRole: UserRole;
  currentUserId: string;
  onStatusUpdate: (
    projectId: string,
    newStatus: ProjectStatus,
    newSubStatus?: undefined,
    phaseCompletedAt?: string,
    phaseKey?: string
  ) => void;
  onViewDetails: (project: Project) => void;
  onAssignTask?: (projectId: string, assigneeId: string, phases?: string[]) => void;
  onDeleteProject?: (projectId: string) => void;
  availableUsers?: User[];
}

export const SupervisorProjectCard = ({
  project,
  userRole,
  currentUserId,
  onStatusUpdate,
  onViewDetails,
  onAssignTask,
  onDeleteProject, // kept in props signature for compatibility
  availableUsers = [],
}: SupervisorProjectCardProps) => {
  const [phaseStates, setPhaseStates] = useState<PhaseStateRecord>({});
  const selectedPhases = (project.supervisorSelectedPhases || []) as SupervisorSubTask[];

  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [showProgressDialog, setShowProgressDialog] = useState(false);

  const canModify = canModifyTask(userRole, project.assignedTo || "", currentUserId);
  const supervisorStatuses: ProjectStatus[] = ["supervisor_pending_assign", "inprogress", "completed"];

  const legacySupervisorSubtasks = [
    "floor_protection",
    "plaster_ceiling",
    "spc",
    "first_painting",
    "carpentry_measure",
    "measure_others",
    "carpentry_install",
    "quartz_measure",
    "quartz_install",
    "glass_measure",
    "glass_install",
    "final_wiring",
    "final_painting",
    "install_others",
    "plumbing",
    "cleaning",
    "defects",
  ] as const;

  const isSupervisorCardActive =
    (project.status === "inprogress" ||
      (Array.isArray(project.supervisorSelectedPhases) && project.supervisorSelectedPhases.length > 0) ||
      (legacySupervisorSubtasks as readonly string[]).includes(project.status as string)) &&
    project.status !== "supervisor_pending_assign" &&
    project.status !== "completed";

  // Managers can always assign
  const canAssignTask = isManager(userRole);

  // use shared StatusBadge for colors

  // Compute progress from new per-phase states if available; fallback to legacy dates
  const computeProgressPercentage = (): number => {
    let selected = (project.supervisorSelectedPhases || []) as SupervisorSubTask[];

    const localStates = phaseStates && Object.keys(phaseStates).length > 0 ? phaseStates : undefined;
    const projectStates = (project as any).supervisorPhaseStates as Record<string, { status: string }> | undefined;

    // If no selected phases, but we have states (local or server), treat those keys as selected for progress purposes
    if (selected.length === 0 && (localStates || projectStates)) {
      const states = (localStates || projectStates)!;
      selected = Object.keys(states) as SupervisorSubTask[];
    }

    const total = selected.length;
    if (total === 0) return 0;

    if (localStates || projectStates) {
      const states = (localStates || projectStates)!;
      const completed = selected.filter((p) => states[p]?.status === 'complete').length;
      return (completed / total) * 100;
    }

    const dates = (project.supervisorPhaseDates as Record<string, string>) || {};
    const completed = selected.filter((p) => !!dates[p]).length;
    return (completed / total) * 100;
  };

  const progressPercentage = computeProgressPercentage();

  const handleSupervisorAssign = (assigneeId: string, phases: string[]) => {
    if (onAssignTask) onAssignTask(project.id, assigneeId, phases);
  };

  // Hide progress button if the task is not yet assigned, no phases selected, or already completed
  const isSupervisorCompleted = project.status === 'completed';
  const shouldHideProgress = !project.assignedTo || selectedPhases.length === 0 || isSupervisorCompleted;

  return (
    <Card className="hover:shadow-md transition-shadow flex flex-col h-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg font-semibold line-clamp-2">{project.title}</CardTitle>
          <div className="flex flex-col items-end gap-1">
            <StatusBadge status={project.status} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 flex flex-col flex-1">
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <UserIcon className="h-4 w-4" />
            <span>{project.client}</span>
          </div>

          {project.assignedTo ? (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <UserIcon className="h-4 w-4" />
              <span>
                Assigned to: {
                  availableUsers?.find((u) => u.id === project.assignedTo)?.name || "Unknown"
                }
              </span>
            </div>
          ) : (
            <div className="flex items-center gap-2 text-sm text-orange-600">
              <UserIcon className="h-4 w-4" />
              <span>Unassigned</span>
            </div>
          )}

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>Created: {formatDate(project.createdAt)}</span>
          </div>

          {project.remarks && (
            <div className="text-sm">
              <span className="text-muted-foreground">Remarks: </span>
              <span
                className="text-foreground line-clamp-2 max-w-full overflow-hidden text-ellipsis"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  maxHeight: '2.5rem'
                }}
                title={project.remarks}
              >
                {project.remarks}
              </span>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{Math.round(progressPercentage)}%</span>
          </div>
          <Progress value={progressPercentage} tone={statusToTone(progressPercentage === 100 ? 'completed' : project.status)} className="h-2" />
        </div>

        <div className="flex-1" />

        <div className="flex gap-2 pt-2 mt-auto">
          <Button variant="outline" size="sm" onClick={() => onViewDetails(project)} className="flex-1">
            <Eye className="h-4 w-4 mr-1" />
            Details
          </Button>

          {/* Assign button visible on card when unassigned (manager only), same as Designer card */}
          {!shouldHideActionButtons(project.status) && isManager(userRole) && !project.assignedTo && (
            <Button variant="default" size="sm" onClick={() => setShowAssignDialog(true)} className="flex-1">
              <UserIcon className="h-4 w-4 mr-1" />
              Assign
            </Button>
          )}

          {/* Keep Complete Subtask on the card */}
          {!shouldHideActionButtons(project.status) && (canModify || isManager(userRole)) && !shouldHideProgress && (
            <Button size="sm" onClick={() => setShowProgressDialog(true)} className="flex-1">
              <ArrowRight className="h-4 w-4 mr-1" />
              Complete Subtask
            </Button>
          )}
        </div>
      </CardContent>

      <SupervisorPhaseDialog
        open={showProgressDialog}
        onOpenChange={setShowProgressDialog}
        selectedPhases={selectedPhases}
        phaseStates={phaseStates}
        onChange={setPhaseStates}
        onTransition={async (phaseKey, to) => {
          try {
            const updated = await Api.transitionSupervisorPhase(project.id, { phaseKey, to });
            if ((updated as any)?.supervisorPhaseStates) {
              setPhaseStates((updated as any).supervisorPhaseStates as PhaseStateRecord);
            }
            if (updated.status === 'completed') {
              // Inform parent that project moved to completed
              onStatusUpdate(project.id, 'completed');
            }
          } catch (e) {
            console.error('Failed to transition phase', e);
          }
        }}
      />

      {(userRole === "manager") && (
        <SupervisorAssignDialog
          open={showAssignDialog}
          onOpenChange={setShowAssignDialog}
          designers={availableUsers?.filter((u) => u.role === "supervisor") || []}
          onConfirm={handleSupervisorAssign}
        />
      )}
    </Card>
  );
};

