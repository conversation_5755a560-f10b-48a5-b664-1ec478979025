import { useQuery } from '@tanstack/react-query';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Api, CaseHistory, CaseSummary } from '@/lib/api';
import { DESIGNER_STATUS_LABELS, SALES_STATUS_LABELS, SUPERVISOR_STATUS_LABELS, SUPERVISOR_SUBTASK_LABELS } from '@/types/project';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft, Copy, User } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { formatDateTime, formatDateTimeLocal, formatIso, formatCurrency } from '@/lib/datetime';

const Section = ({ title, children }: { title: string; children: React.ReactNode }) => (
  <div className="space-y-2">
    <h3 className="text-base font-semibold">{title}</h3>
    <div className="space-y-2">{children}</div>
  </div>
);

const TimelineItem = ({ ts, who, label, sourceId, onCopy, eventType }: {
  ts: string;
  who?: string | null;
  label: string;
  sourceId?: string;
  onCopy?: (id: string) => void;
  eventType?: string;
}) => {
  const getEventIcon = (type?: string) => {
    switch (type) {
      case 'task_created': return '📝';
      case 'assignment_changed': return '👤';
      case 'status_changed': return '🔄';
      case 'designer_revision': return '🎨';
      case 'supervisor_task_completed': return '✅';
      case 'supervisor_task_assigned': return '📋';
      case 'supervisor_phase_status_changed': return '🔧';
      default: return '📌';
    }
  };

  const getActorDisplay = (who?: string | null, eventType?: string) => {
    if (!who) {
      // For auto-created tasks, show SYSTEM
      if (eventType === 'task_created') {
        return 'SYSTEM';
      }
      return null;
    }
    return who;
  };

  const actor = getActorDisplay(who, eventType);

  return (
    <div className="flex items-start gap-3 py-3 border-l-2 border-muted pl-4 ml-2">
      <div className="text-xs text-muted-foreground min-w-[180px] font-mono">
        {formatDateTimeLocal(ts)}
      </div>
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <span className="text-sm">{getEventIcon(eventType)}</span>
          <div className="text-sm font-medium text-foreground">{label}</div>
        </div>
        {actor && (
          <div className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
            <User className="h-3 w-3" />
            <span className={actor === 'SYSTEM' ? 'font-mono text-blue-600 font-semibold' : ''}>
              {actor === 'SYSTEM' ? 'SYSTEM' : `by ${actor}`}
            </span>
          </div>
        )}
      </div>
      {sourceId && onCopy && (
        <Tooltip>
          <TooltipTrigger asChild>
            <button onClick={() => onCopy(sourceId)} className="text-xs text-muted-foreground hover:text-foreground">
              <Copy className="h-3 w-3" />
            </button>
          </TooltipTrigger>
          <TooltipContent>Copy source ID</TooltipContent>
        </Tooltip>
      )}
    </div>
  );
};

const CaseDetails = () => {
  const { caseId } = useParams();
  const { toast } = useToast();

  const summary = useQuery<{ data: CaseSummary }>({
    queryKey: ['case', caseId],
    queryFn: async () => {
      const data = await Api.getCase(caseId!);
      return { data };
    },
    enabled: !!caseId,
  });

  const history = useQuery<{ data: CaseHistory }>({
    queryKey: ['case-history', caseId],
    queryFn: async () => {
      const data = await Api.getCaseHistory(caseId!);
      return { data };
    },
    enabled: !!caseId,
  });

  const events = history.data?.data.events || [];

  const labelForRoleStatus = (role: 'sales'|'designer'|'supervisor', status?: string) => {
    if (!status) return '—';
    if (role === 'sales') return (SALES_STATUS_LABELS as Record<string, string>)[status] || status;
    if (role === 'designer') return (DESIGNER_STATUS_LABELS as Record<string, string>)[status] || status;
    if (role === 'supervisor') return (SUPERVISOR_STATUS_LABELS as Record<string, string>)[status] || status;
    return status;
  };

  const humanizeSupervisorPhase = (phase?: string) => {
    return (phase && (SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]) || '';
  };

  const labelForEvent = (e: CaseHistory['events'][number]) => {
    const payload = e.payload as any;

    switch (e.type) {
      case 'task_created':
        return 'Task created';
      case 'assignment_changed':
        const fromUser = payload?.fromUserId ? `from ${payload.fromUserId}` : 'from unassigned';
        const toUser = payload?.toUserId ? `to ${payload.toUserId}` : 'to unassigned';
        return `Assignment changed ${fromUser} ${toUser}`;
      case 'status_changed':
        const fromStatus = payload?.from ? labelForRoleStatus(e.role, payload.from) : '';
        const toStatus = payload?.to ? labelForRoleStatus(e.role, payload.to) : '';
        let statusChange = `Status changed`;
        if (fromStatus && toStatus) {
          statusChange += ` from ${fromStatus} to ${toStatus}`;
        } else if (toStatus) {
          statusChange += ` to ${toStatus}`;
        }
        // Add sales sub-status changes
        if (payload?.salesSubStatusFrom || payload?.salesSubStatusTo) {
          const fromSub = payload?.salesSubStatusFrom || 'none';
          const toSub = payload?.salesSubStatusTo || 'none';
          statusChange += ` (${fromSub} → ${toSub})`;
        }
        return statusChange;
      case 'designer_revision':
        const kind = payload?.kind || '';
        return `Revision submitted (${kind.toUpperCase()})`;
      case 'supervisor_task_completed':
        const phase = payload?.phase;
        return `Completed ${humanizeSupervisorPhase(phase)}`;
      case 'supervisor_task_assigned':
        const selectedCount = payload?.selected?.length || 0;
        return `Assigned ${selectedCount} subtask${selectedCount !== 1 ? 's' : ''}`;
      case 'supervisor_phase_status_changed':
        const phaseName = humanizeSupervisorPhase(payload?.phase);
        const fromPhaseStatus = payload?.fromStatus || '';
        const toPhaseStatus = payload?.toStatus || '';
        return `${phaseName}: ${fromPhaseStatus} → ${toPhaseStatus}`;
      case 'supervisor_phase_transition':
        // Handle legacy event type as well
        const legacyPhaseName = humanizeSupervisorPhase(payload?.phaseKey);
        const legacyFromStatus = payload?.from || '';
        const legacyToStatus = payload?.to || '';
        return `${legacyPhaseName}: ${legacyFromStatus} → ${legacyToStatus}`;
      case 'sales_updated':
      case 'designer_updated':
        const changes = payload?.changes || [];
        if (changes.length === 1) {
          const change = changes[0];
          return `Updated ${change.field}`;
        } else if (changes.length > 1) {
          return `Updated ${changes.length} fields`;
        }
        return 'Updated';
      default:
        return e.type.replace(/_/g, ' ');
    }
  };

  const handleCopySourceId = async (id: string) => {
    try {
      await navigator.clipboard.writeText(id);
      toast({ description: 'Copied source ID' });
    } catch (e) {
      toast({ description: 'Failed to copy', variant: 'destructive' });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-6 py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link to="/case-history" className="inline-flex items-center text-sm">
              <ArrowLeft className="h-4 w-4 mr-2" /> Back to Case History
            </Link>
            <div>
              <h1 className="text-2xl font-bold">{summary.data?.data.title || 'Case Details'}</h1>
              <p className="text-muted-foreground">{summary.data?.data.client}</p>
            </div>
          </div>
          {summary.data?.data.salesAmount ? (
            <Badge variant="secondary">{formatCurrency(summary.data?.data.salesAmount)}</Badge>
          ) : null}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Roles</CardTitle>
          </CardHeader>
          {summary.isLoading && (
            <div className="px-6 pb-4 text-sm text-muted-foreground">Loading case…</div>
          )}
          {summary.isError && (
            <div className="px-6 pb-4 text-sm">
              <div className="text-red-500 mb-2">Failed to load case</div>
              <Button variant="outline" size="sm" onClick={() => summary.refetch?.()}>Retry</Button>
            </div>
          )}
          <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {(['sales', 'designer', 'supervisor'] as const).map(role => {
              const item = (summary.data?.data.roles as Record<'sales'|'designer'|'supervisor', { id?: string; status?: string; assignedTo?: string | null } | null>)?.[role];
              return (
                <div key={role} className="space-y-1">
                  <div className="text-sm font-medium capitalize">{role}</div>
                  <div className="text-sm text-muted-foreground">Status: {labelForRoleStatus(role, item?.status)}</div>
                  <div className="text-sm text-muted-foreground">Assignee: {item?.assignedToName || item?.assignedTo || '—'}</div>
                  {!!item?.id && (
                    <Link className="text-xs underline" to={`/projects/${item.id}`}>Open task</Link>
                  )}
                </div>
              );
            })}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Timeline</CardTitle>
            {history.isLoading && (
              <div className="text-sm text-muted-foreground">Loading history…</div>
            )}
            {history.isError && (
              <div className="text-sm">
                <div className="text-red-500 mb-2">Failed to load history</div>
                <Button variant="outline" size="sm" onClick={() => history.refetch?.()}>Retry</Button>
              </div>
            )}
            {!summary.isLoading && !history.isLoading && events.length === 0 && (
              <div className="text-sm text-muted-foreground">No history yet for this case.</div>
            )}
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Sales */}
            <Section title="Sales">
              {events.filter(e => e.role === 'sales').length === 0 ? (
                <div className="text-sm text-muted-foreground">No events yet.</div>
              ) : (
                events.filter(e => e.role === 'sales').map(e => (
                  <TimelineItem
                    key={e.id}
                    ts={e.timestamp}
                    who={e.user?.name}
                    label={labelForEvent(e)}
                    sourceId={e.sourceId}
                    onCopy={handleCopySourceId}
                    eventType={e.type}
                  />
                ))
              )}
            </Section>
            <Separator />
            {/* Designer */}
            <Section title="Designer">
              {events.filter(e => e.role === 'designer').length === 0 ? (
                <div className="text-sm text-muted-foreground">No events yet.</div>
              ) : (
                events.filter(e => e.role === 'designer').map(e => (
                  <TimelineItem
                    key={e.id}
                    ts={e.timestamp}
                    who={e.user?.name}
                    label={labelForEvent(e)}
                    sourceId={e.sourceId}
                    onCopy={handleCopySourceId}
                    eventType={e.type}
                  />
                ))
              )}
            </Section>
            <Separator />
            {/* Site Supervisor */}
            <Section title="Site Supervisor">
              {events.filter(e => e.role === 'supervisor').length === 0 ? (
                <div className="text-sm text-muted-foreground">No events yet.</div>
              ) : (
                events.filter(e => e.role === 'supervisor').map(e => (
                  <TimelineItem
                    key={e.id}
                    ts={e.timestamp}
                    who={e.user?.name}
                    label={labelForEvent(e)}
                    sourceId={e.sourceId}
                    onCopy={handleCopySourceId}
                    eventType={e.type}
                  />
                ))
              )}
            </Section>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CaseDetails;

